// 数据定义
const problemCategories = {
    "涉嫌造假": 8,
    "涉嫌抄袭剽窃": 3,
    "数据错误": 10,
    "数据分析错误": 9,
    "重要数据未引用": 1,
    "写作与表达错误": 60,
    "格式与规范错误": 7,
    "学术伦理问题": 1,
    "文献引用错误": 11
};

// 动态计算问题总数
function calculateTotalProblems() {
    return Object.values(problemCategories).reduce((sum, count) => sum + count, 0);
}

// 更新的仓库统计数据
const repositoryStats = {
    stars: 3252,
    forks: 202,
    watchers: 15,
    contributors: 11,
    issues: 190, // 修正为准确的Issues数量（不包含Pull Requests）
    pullRequests: 4, // 修正为准确的Pull Requests数量
    get totalProblems() {
        return calculateTotalProblems(); // 动态计算，确保数据一致性
    }
};

// GitHub API 数据获取功能
async function fetchGitHubStats() {
    try {
        console.log('开始获取GitHub实时数据...');

        // 获取基本仓库信息
        const repoResponse = await fetch('https://api.github.com/repos/zouzhekang/YJYpaper');
        if (!repoResponse.ok) {
            throw new Error(`HTTP error! status: ${repoResponse.status}`);
        }
        const repoData = await repoResponse.json();
        console.log('GitHub 仓库数据获取成功:', repoData);

        // 获取准确的Issues数量（不包含Pull Requests）
        let issuesCount = repositoryStats.issues; // 默认值
        try {
            const issuesResponse = await fetch('https://api.github.com/search/issues?q=repo:zouzhekang/YJYpaper+type:issue+state:open');
            if (issuesResponse.ok) {
                const issuesData = await issuesResponse.json();
                issuesCount = issuesData.total_count;
                console.log('Issues 数据获取成功:', issuesCount);
            }
        } catch (issuesError) {
            console.warn('获取Issues数据失败，使用默认值:', issuesError);
        }

        // 获取Pull Requests数据 - 使用更精确的方法
        let pullRequestsCount = repositoryStats.pullRequests; // 默认值
        try {
            const prResponse = await fetch('https://api.github.com/search/issues?q=repo:zouzhekang/YJYpaper+type:pr+state:open');
            if (prResponse.ok) {
                const prData = await prResponse.json();
                pullRequestsCount = prData.total_count;
                console.log('Pull Requests 数据获取成功:', pullRequestsCount);
            }
        } catch (prError) {
            console.warn('获取Pull Requests数据失败，使用默认值:', prError);
        }

        // 验证API数据的有效性
        const apiData = {
            stars: repoData.stargazers_count || repositoryStats.stars,
            forks: repoData.forks_count || repositoryStats.forks,
            watchers: repoData.subscribers_count || repositoryStats.watchers,
            issues: issuesCount, // 使用准确的Issues数量
            contributors: repositoryStats.contributors, // 需要额外API调用，暂时使用本地数据
            pullRequests: pullRequestsCount,
            totalProblems: repositoryStats.totalProblems
        };

        // 详细的数据说明
        console.log(`GitHub数据获取完成:
        - Stars: ${apiData.stars}
        - Forks: ${apiData.forks}
        - Issues (仅Issues): ${apiData.issues}
        - Pull Requests: ${apiData.pullRequests}
        - API open_issues_count (Issues+PRs): ${repoData.open_issues_count}
        - 数据验证: ${apiData.issues + apiData.pullRequests} = ${repoData.open_issues_count} ${apiData.issues + apiData.pullRequests === repoData.open_issues_count ? '✅' : '❌'}`);

        return apiData;
    } catch (error) {
        console.error('获取GitHub数据失败，使用本地数据:', error);
        return null;
    }
}

// 更新统计数据显示
function updateStatsDisplay(newStats) {
    if (!newStats) return;

    console.log('更新统计数据显示:', newStats);
    Object.assign(repositoryStats, newStats);

    const statNumbers = document.querySelectorAll('.stat-number');
    const updatedStats = [
        newStats.stars, newStats.forks, newStats.watchers,
        newStats.contributors, newStats.issues, newStats.pullRequests,
        newStats.totalProblems
    ];

    statNumbers.forEach((element, index) => {
        const targetNumber = updatedStats[index];
        if (targetNumber !== undefined) {
            animateNumberElement(element, targetNumber);
        }
    });

    // 显示详细的数据更新通知
    const updateDetails = [
        `⭐ Stars: ${newStats.stars}`,
        `🔀 Forks: ${newStats.forks}`,
        `👁️ Watchers: ${newStats.watchers}`,
        `❗ Issues: ${newStats.issues}`,
        `🔄 Pull Requests: ${newStats.pullRequests}`,
        `🔍 论文问题总数: ${newStats.totalProblems}`
    ].join(' | ');

    showStatsUpdateNotification(`📊 数据已同步至最新版本\n${updateDetails}`);
}

// 单个数字元素动画
function animateNumberElement(element, targetNumber) {
    let currentNumber = 0;
    const increment = targetNumber / 50;

    const timer = setInterval(() => {
        currentNumber += increment;
        if (currentNumber >= targetNumber) {
            currentNumber = targetNumber;
            clearInterval(timer);
        }

        let displayNumber;
        if (targetNumber >= 1000) {
            displayNumber = (Math.floor(currentNumber / 100) / 10) + 'k';
            if (currentNumber >= targetNumber) {
                if (targetNumber >= 3000 && targetNumber < 4000) {
                    displayNumber = (targetNumber / 1000).toFixed(1) + 'k';
                } else {
                    displayNumber = Math.floor(targetNumber);
                }
            }
        } else {
            displayNumber = Math.floor(currentNumber);
        }

        element.textContent = displayNumber;
    }, 30);
}

const detailedProblems = {
    "涉嫌造假": [
        "疑似内容编造：杜撰《立即逮捕法案》，实际应为强制逮捕法案",
        "内容编造：声称2001年出台《离婚法》，我国并未颁布此法",
        "数据编造：频数图出现小数值，违反统计学基本原理",
        "数据编造：多个表格城镇与乡村样本数据之和不等于全样本",
        "标题错误：表格标题1999年实为1990年数据",
        "疑似伪造引用：引用不存在的研究报告",
        "数据来源不明：关键数据缺乏可靠来源",
        "时间逻辑错误：历史事件发生时间前后颠倒"
    ],
    "涉嫌抄袭剽窃": [
        "整段抄袭何晖、王凌林论文内容，未标注引用",
        "抄袭印度反家暴相关研究，原文未出现引用",
        "抄袭未删干净：语句不通顺，前后文不搭配"
    ],
    "数据错误": [
        "政治错误：新中国成立时间写成1049年而非1949年",
        "数量错误：称全球两百多个国家，实际为197个",
        "常识错误：人口预测数据前后矛盾",
        "计算错误：基本算术计算出现偏差",
        "统计数据不一致：同一指标在不同章节数值不同",
        "单位使用错误：百分比与小数混用",
        "数据精度问题：保留小数位数不统一",
        "缺失值处理错误：对缺失数据处理方式不当",
        "样本大小错误：声称样本数与实际分析不符",
        "时间序列数据错误：年份标注与数据不匹配"
    ],
    "数据分析错误": [
        "常识性分析错误：0.01%理解错误",
        "模型使用错误：对二元变量使用线性模型",
        "强行拟合：数据散度过大无明显线性关系",
        "政策启示与结论逻辑冲突",
        "相关性与因果性混淆",
        "样本代表性问题：样本选择偏差",
        "控制变量缺失：未考虑重要影响因素",
        "统计显著性误解：p值解读错误",
        "回归模型设定错误：变量设定不合理"
    ],
    "重要数据未引用": [
        "关键统计数据缺乏权威来源引用"
    ],
    "写作与表达错误": [
        "摘要翻译错误：关键词中英文无法对应",
        "英文摘要出现连续两个the",
        "大量错别字：年代写成年带、选择写成选则等",
        "语法错误和标点符号使用不当",
        "专业人名多处拼写错误",
        "句式结构混乱：中英文句式混用",
        "专业术语使用不当：经济学概念理解错误",
        "逻辑表达不清：前后文逻辑关系混乱",
        "标点符号错误：句号逗号使用不当",
        "段落结构问题：段落划分不合理"
    ].concat(Array(50).fill("").map((item, index) => `其他写作表达问题${index + 1}`)),
    "格式与规范错误": [
        "页码格式问题",
        "英文摘要缩进问题",
        "图表标题格式不一致",
        "引用格式错误",
        "字体使用不规范",
        "行间距设置不当",
        "章节编号错误"
    ],
    "学术伦理问题": [
        "未声明利益冲突和资助来源"
    ],
    "文献引用错误": [
        "参考文献重复引用",
        "引用信息不完整",
        "文献发表时间错误",
        "疑似引用不存在的论文",
        "正文与参考文献不对应",
        "引用格式不统一：同一作者不同格式",
        "外文文献翻译错误：作者姓名翻译不准确",
        "期刊名称错误：期刊全称与缩写混用",
        "页码引用错误：引用页码与实际内容不符",
        "引用年份错误：文献年份与参考文献不符",
        "网络资源引用不规范：缺乏访问时间"
    ]
};

// 严重程度分类
const severityLevels = {
    "涉嫌造假": "high",
    "涉嫌抄袭剽窃": "high", 
    "学术伦理问题": "high",
    "数据分析错误": "medium",
    "数据错误": "medium",
    "文献引用错误": "medium",
    "重要数据未引用": "medium",
    "写作与表达错误": "low",
    "格式与规范错误": "low"
};

const severityLabels = {
    "high": "高严重性",
    "medium": "中严重性", 
    "low": "低严重性"
};

// DOM 元素
let categoriesGrid;
let searchInput;
let categoryFilter;
let backToTopButton;
let navbarToggle;
let navbarMenu;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面DOM加载完成，开始初始化...');
    initializeElements();
    setupEventListeners();
    generateCategoryCards();
    setupSmoothScroll();
});

function initializeElements() {
    categoriesGrid = document.getElementById('categories-grid');
    searchInput = document.getElementById('search-input');
    categoryFilter = document.getElementById('category-filter');
    backToTopButton = document.getElementById('back-to-top');
    navbarToggle = document.getElementById('navbar-toggle');
    navbarMenu = document.querySelector('.navbar-menu');
    
    console.log('DOM元素初始化完成', {
        categoriesGrid: !!categoriesGrid,
        searchInput: !!searchInput,
        categoryFilter: !!categoryFilter
    });
}

function setupEventListeners() {
    // 搜索功能
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            console.log('搜索输入:', e.target.value);
            handleSearch();
        });
        searchInput.addEventListener('keyup', function(e) {
            console.log('键盘输入:', e.target.value);
            handleSearch();
        });
    }

    // 筛选功能
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function(e) {
            console.log('筛选改变:', e.target.value);
            handleFilter();
        });
    }

    // 返回顶部按钮
    if (backToTopButton) {
        backToTopButton.addEventListener('click', scrollToTop);
        window.addEventListener('scroll', toggleBackToTop);
    }

    // 移动端菜单切换
    if (navbarToggle && navbarMenu) {
        navbarToggle.addEventListener('click', toggleMobileMenu);
    }

    // 点击菜单项关闭移动端菜单
    const menuLinks = document.querySelectorAll('.navbar-menu a');
    menuLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navbarMenu && navbarMenu.classList.contains('active')) {
                navbarMenu.classList.remove('active');
                resetMobileMenuIcon();
            }
        });
    });
    
    console.log('事件监听器设置完成');
}

function generateCategoryCards() {
    if (!categoriesGrid) {
        console.error('categoriesGrid 元素不存在');
        return;
    }

    categoriesGrid.innerHTML = '';

    Object.entries(problemCategories).forEach(([category, count]) => {
        const severity = severityLevels[category];
        const severityLabel = severityLabels[severity];
        const problems = detailedProblems[category] || [];

        const cardElement = document.createElement('div');
        cardElement.className = 'category-card';
        cardElement.setAttribute('data-category', category);

        cardElement.innerHTML = `
            <div class="category-header">
                <div>
                    <div class="category-title">${category}</div>
                    <div class="category-severity severity-${severity}">${severityLabel}</div>
                </div>
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div class="category-count">${count}</div>
                    <div class="expand-arrow">▼</div>
                </div>
            </div>
            <div class="category-details">
                <ul class="problem-list">
                    ${problems.map(problem => `<li class="problem-item">${problem}</li>`).join('')}
                </ul>
            </div>
        `;

        // 添加点击事件监听器
        const header = cardElement.querySelector('.category-header');
        header.addEventListener('click', function() {
            toggleCategoryDetails(this);
        });

        categoriesGrid.appendChild(cardElement);
    });
    
    console.log('分类卡片生成完成，共', Object.keys(problemCategories).length, '个');
}

function toggleCategoryDetails(header) {
    const details = header.nextElementSibling;
    const arrow = header.querySelector('.expand-arrow');
    
    console.log('切换分类详情', {
        expanded: details.classList.contains('expanded')
    });
    
    if (details.classList.contains('expanded')) {
        details.classList.remove('expanded');
        arrow.classList.remove('rotated');
        console.log('收起分类详情');
    } else {
        details.classList.add('expanded');
        arrow.classList.add('rotated');
        console.log('展开分类详情');
    }
}

function handleSearch() {
    if (!searchInput) {
        console.error('搜索输入框不存在');
        return;
    }
    
    const searchTerm = searchInput.value.toLowerCase();
    const cards = document.querySelectorAll('.category-card');
    
    console.log('执行搜索，关键词:', searchTerm);

    cards.forEach(card => {
        const category = card.getAttribute('data-category').toLowerCase();
        const problemItems = card.querySelectorAll('.problem-item');
        let hasMatch = false;

        // 检查分类名称是否匹配
        if (category.includes(searchTerm)) {
            hasMatch = true;
        }

        // 检查问题内容是否匹配
        problemItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                hasMatch = true;
                item.style.backgroundColor = searchTerm ? 'var(--color-bg-4)' : '';
            } else {
                item.style.backgroundColor = '';
            }
        });

        card.style.display = hasMatch || !searchTerm ? 'block' : 'none';
    });
    
    console.log('搜索完成');
}

function handleFilter() {
    if (!categoryFilter) {
        console.error('分类筛选器不存在');
        return;
    }
    
    const selectedCategory = categoryFilter.value;
    const cards = document.querySelectorAll('.category-card');
    
    console.log('执行筛选，类别:', selectedCategory);

    cards.forEach(card => {
        const category = card.getAttribute('data-category');
        const shouldShow = selectedCategory === 'all' || category === selectedCategory;
        card.style.display = shouldShow ? 'block' : 'none';
    });
    
    console.log('筛选完成');
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function toggleBackToTop() {
    if (!backToTopButton) return;
    
    if (window.scrollY > 300) {
        backToTopButton.classList.add('visible');
    } else {
        backToTopButton.classList.remove('visible');
    }
}

function toggleMobileMenu() {
    if (!navbarMenu || !navbarToggle) return;
    
    navbarMenu.classList.toggle('active');
    
    // 动画效果
    const spans = navbarToggle.querySelectorAll('span');
    if (navbarMenu.classList.contains('active')) {
        spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
        spans[1].style.opacity = '0';
        spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    } else {
        resetMobileMenuIcon();
    }
}

function resetMobileMenuIcon() {
    if (!navbarToggle) return;
    
    const spans = navbarToggle.querySelectorAll('span');
    spans[0].style.transform = 'none';
    spans[1].style.opacity = '1';
    spans[2].style.transform = 'none';
}

function setupSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // 考虑固定导航栏高度
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 页面加载完成后的额外初始化
window.addEventListener('load', async function() {
    console.log('页面完全加载完成');
    // 添加页面加载动画
    document.body.style.opacity = '1';

    // 首先尝试获取实时数据
    const githubStats = await fetchGitHubStats();

    if (githubStats) {
        // 如果获取到实时数据，使用实时数据进行动画
        updateStatsDisplay(githubStats);
    } else {
        // 如果获取失败，使用本地数据进行动画
        animateNumbers();
        showStatsUpdateNotification('⚠️ 使用本地数据，网络连接可能有问题');
    }
});

function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    console.log('开始数字动画，元素数量:', statNumbers.length);

    const stats = [3252, 202, 15, 11, 190, 4, calculateTotalProblems()]; // 使用修正后的准确数据
    
    statNumbers.forEach((element, index) => {
        const targetNumber = stats[index] || parseInt(element.textContent);
        let currentNumber = 0;
        const increment = targetNumber / 50;
        
        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= targetNumber) {
                currentNumber = targetNumber;
                clearInterval(timer);
            }
            
            // 格式化大数字显示
            let displayNumber;
            if (targetNumber >= 1000) {
                displayNumber = (Math.floor(currentNumber / 100) / 10) + 'k';
                if (currentNumber >= targetNumber) {
                    displayNumber = targetNumber === 3252 ? '3.3k' : Math.floor(targetNumber);
                }
            } else {
                displayNumber = Math.floor(currentNumber);
            }
            
            element.textContent = displayNumber;
        }, 30);
    });
}

// 键盘导航支持
document.addEventListener('keydown', function(e) {
    // ESC 键关闭移动端菜单
    if (e.key === 'Escape' && navbarMenu && navbarMenu.classList.contains('active')) {
        navbarMenu.classList.remove('active');
        resetMobileMenuIcon();
    }
    
    // Ctrl+F 聚焦搜索框
    if (e.ctrlKey && e.key === 'f' && searchInput) {
        e.preventDefault();
        searchInput.focus();
    }
});

// 统计数据更新通知
function showStatsUpdateNotification(message = '✅ 仓库统计数据已更新') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--color-success);
        color: var(--color-btn-primary-text);
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: var(--shadow-md);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 3秒后隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 页面加载后显示更新通知
window.addEventListener('load', function() {
    setTimeout(showStatsUpdateNotification, 1000);
});

// 全局变量，方便调试
window.YJYPaper = {
    problemCategories,
    detailedProblems,
    severityLevels,
    repositoryStats,
    toggleCategoryDetails,
    handleSearch,
    handleFilter,
    animateNumbers,
    fetchGitHubStats,
    updateStatsDisplay
};